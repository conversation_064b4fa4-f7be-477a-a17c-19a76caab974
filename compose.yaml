# Docker Compose configuration for the Tag Application microservices
# Visit https://docs.docker.com/go/compose-spec-reference/ for more details

services:
  auth:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        SERVICE_NAME: auth
      target: final
    ports:
      - "8100:8080"
    environment:
      - DB_HOST=db
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_NAME=tag_app
      - DB_PASSWORD_FILE=/run/secrets/db-password
    depends_on:
      db:
        condition: service_healthy
    secrets:
      - db-password
    networks:
      - tag-network

  # user:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #     args:
  #       SERVICE_NAME: user
  #     target: final
  #   ports:
  #     - "8101:8080"
  #   environment:
  #     - DB_HOST=db
  #     - DB_PORT=5432
  #     - DB_USER=postgres
  #     - DB_NAME=tag_app
  #     - DB_PASSWORD_FILE=/run/secrets/db-password
  #   depends_on:
  #     db:
  #       condition: service_healthy
  #   secrets:
  #     - db-password
  #   networks:
  #     - tag-network

  # chat:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #     args:
  #       SERVICE_NAME: chat
  #     target: final
  #   ports:
  #     - "8102:8080"
  #   environment:
  #     - DB_HOST=db
  #     - DB_PORT=5432
  #     - DB_USER=postgres
  #     - DB_NAME=tag_app
  #     - DB_PASSWORD_FILE=/run/secrets/db-password
  #   depends_on:
  #     db:
  #       condition: service_healthy
  #   secrets:
  #     - db-password
  #   networks:
  #     - tag-network

  # payment:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #     args:
  #       SERVICE_NAME: payment
  #     target: final
  #   ports:
  #     - "8103:8080"
  #   environment:
  #     - DB_HOST=db
  #     - DB_PORT=5432
  #     - DB_USER=postgres
  #     - DB_NAME=tag_app
  #     - DB_PASSWORD_FILE=/run/secrets/db-password
  #   depends_on:
  #     db:
  #       condition: service_healthy
  #   secrets:
  #     - db-password
  #   networks:
  #     - tag-network

  db:
    image: postgres:17.5-alpine
    restart: always
    user: postgres
    secrets:
      - db-password
    volumes:
      - db-data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_DB=tag_app
      - POSTGRES_PASSWORD_FILE=/run/secrets/db-password
    expose:
      - 5432
    healthcheck:
      test: ["CMD", "pg_isready"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - tag-network

volumes:
  db-data:

secrets:
  db-password:
    file: db/password.txt

networks:
  tag-network:
    driver: bridge