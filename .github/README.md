# Tag server

Server of Tag Application

> Links:
> [Telegram](https://t.me/alternati0) /
> [GitHub](https://github.com/alternatio) /
> [Website](https://prolesfero-portfolio.netlify.app/)

## Tools

This project uses the following tools:

1. [Golang](https://go.dev/) – Programming language
2. [Fiber](https://gofiber.io/) – Web framework
3. [PostgreSQL](https://www.postgresql.org/) – Database
4. [Docker](https://www.docker.com/) – Containerization
5. [Docker Compose](https://docs.docker.com/compose/) – Orchestration
6. [GitHub Actions](https://github.com/features/actions) – CI/CD
7. [Swagger](https://swagger.io/) – API documentation

## Microservices

All microservices are located in the `cmd` directory.

1. `auth` – Authentication service
2. `user` – User service
3. `chat` – Chat service
4. `payment` – Payment service

## Folder Structure

This is a folder structure of the project:

```bash
microservices-project/
├── /cmd
│   ├── /service1
│   │   └── main.go          # Точка входа для сервиса 1
│   ├── /service2
│   │   └── main.go          # Точка входа для сервиса 2
├── /internal
|   ├── /services
│   │   ├── /service1
│   │   │   ├── /api         # HTTP handlers (Fiber)
│   │   │   ├── /models      # Структуры данных
│   │   │   └── /service     # Бизнес-логика
│   │   ├── /service2
│   │   │   ├── /api
│   │   │   ├── /models
│   │   │   └── /service
│   └── /shared              # Общие библиотеки (конфиг, логгер, утилиты)
│       ├── /config
│       ├── /logger
│       └── /utils
├── /pkg                     # Общие пакеты, которые могут быть использованы вне проекта
├── /api                     # Спецификации API (Swagger/OpenAPI)
├── /scripts                 # Скрипты для деплоя, миграций и т.д.
├── go.mod                   # Модуль Go для всего проекта
├── go.sum
├── Dockerfile               # Dockerfile для каждого сервиса или общий
├── docker-compose.yml       # Для локального запуска всех сервисов
├── .github
│   └── /workflows           # CI/CD пайплайны
└── README.md
```

## How to use

1. Clone the repository
2. Create `db/password.txt` file and put the password in it
3. Run `docker compose up --build`
4. Open `http://localhost:8100/api/v1/swagger/index.html` in your browser
5. Use the API
