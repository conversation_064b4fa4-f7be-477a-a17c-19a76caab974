# Use the official Go image as the base image for building
FROM golang:1.24.5-alpine AS builder

# Set the working directory inside the container
WORKDIR /app

# Copy go.mod and go.sum files to cache dependencies
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy the entire project source code
COPY . .

# Build the specific microservice (replace SERVICE_NAME with auth, user, chat, or payment at build time)
ARG SERVICE_NAME
RUN go build -o /app/main ./cmd/$SERVICE_NAME/main.go

# Use a smaller alpine image for the final runtime
FROM alpine:3.22 AS final

# Set the working directory
WORKDIR /app

# Copy the compiled binary from the builder stage
COPY --from=builder /app/main .

# Expose the port (default to 8080, can be overridden for each service)
EXPOSE 8080

# Command to run the microservice
CMD ["./main"]